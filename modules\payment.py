import stripe
import os
import logging
from datetime import datetime
from dotenv import load_dotenv
from flask import Blueprint, request, render_template, jsonify, url_for, current_app, session
from flask_wtf.csrf import CSRFProtect
from modules.models import db, CustomerData
from sqlalchemy.exc import SQLAlchemyError
from flask_mail import Message, Mail
from modules.download import generate_download_link

# Set up logging to console for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Blueprint with explicit template folder
payment_bp = Blueprint('payment', __name__,
                      template_folder='../Templates')

# Load environment variables
load_dotenv()

# Set up Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
PRICE_ID = os.getenv("STRIPE_PRICE_ID")
STRIPE_DOMAIN = os.getenv("STRIPE_DOMAIN")

logger.info("Payment module initialized")

def send_confirmation_email(customer_email, flask_session_id):
    """
    Send confirmation email with download link

    Args:
        customer_email (str): Customer's email address
        flask_session_id (str): Flask session ID for download verification

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        mail = current_app.extensions['mail']
        download_link = generate_download_link(flask_session_id)
        if not download_link:
            logger.error("Could not generate download link for email")
            return False

        # Use the properly formatted sender from app config
        msg = Message(
            "Ordrebekreftelse",
            sender=current_app.config['MAIL_DEFAULT_SENDER'],
            recipients=[customer_email]
        )

        msg.body = f"Takk for din bestilling! Du kan laste ned din e-bok her: {download_link}"
        
        # Render the template
        html_content = render_template(
            'ordrebekreftelse-email.html',
            download_link=download_link,
            email=customer_email
        )
        
        # Process the HTML to add line breaks to base64 encoded images
        # This prevents "message has lines too long for transport" errors
        import re
        
        def add_line_breaks_to_base64(match):
            """Add line breaks every 76 characters to base64 content"""
            prefix = match.group(1)  # The part before the base64 data
            base64_data = match.group(2)  # The actual base64 data
            
            # Add a newline every 76 characters in the base64 data
            # 76 is a standard line length for base64 in MIME
            chunks = [base64_data[i:i+76] for i in range(0, len(base64_data), 76)]
            formatted_base64 = '\n'.join(chunks)
            
            # Return the prefix followed by the formatted base64 data
            return prefix + formatted_base64
        
        # Find all base64 image data and add line breaks
        pattern = r'(data:image\/[^;]+;base64,)([A-Za-z0-9+/=]+)'
        html_content = re.sub(pattern, add_line_breaks_to_base64, html_content)
        
        msg.html = html_content
        mail.send(msg)
        logger.info(f"Confirmation email sent to {customer_email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send confirmation email: {str(e)}")
        return False

def create_customer_record(verification_result, stripe_session_id):
    """
    Create a new customer record in the database

    Args:
        verification_result (dict): Payment verification result from Stripe
        stripe_session_id (str): Stripe session ID

    Returns:
        tuple: (success: bool, message: str)
    """
    try:
        # Get Flask session ID if available
        flask_session_id = session.get('session_id', None)
        if not flask_session_id:
            logger.warning("No Flask session ID found in Flask session")

        customer_data = CustomerData(
            customer_name=verification_result.get('customer_name', 'Unknown'),
            customer_email=verification_result.get('customer_email', 'Unknown'),
            payment_status=verification_result.get('payment_status', 'paid'),
            download_code=stripe_session_id,
            timestamp=datetime.now(),
            download_count=0,
            email_sent=False,
            flask_session_id=flask_session_id
        )

        logger.debug(f"Attempting to create customer record: {customer_data}")
        db.session.add(customer_data)
        db.session.commit()

        # Send confirmation email
        if send_confirmation_email(customer_data.customer_email, flask_session_id):
            customer_data.email_sent = True
            db.session.commit()

        logger.info(f"Successfully created customer record for {customer_data.customer_email} with Flask session ID {flask_session_id}")
        return True, "Customer record created successfully"

    except SQLAlchemyError as e:
        db.session.rollback()
        error_msg = f"Database error while creating customer record: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

    except Exception as e:
        db.session.rollback()
        error_msg = f"Unexpected error while creating customer record: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def verify_stripe_payment(stripe_session_id):
    """
    Verify Stripe payment status

    Args:
        stripe_session_id (str): Stripe Checkout Session ID

    Returns:
        dict: Verification result with status and details
    """
    try:
        # Retrieve the session from Stripe
        stripe_session = stripe.checkout.Session.retrieve(stripe_session_id)

        # Log the payment status for debugging
        logger.info(f"Payment verification for session {stripe_session_id}: status={stripe_session.payment_status}")

        # Accept any payment status as valid
        # This will allow Klarna and other payment methods to work
        return {
            'verified': True,
            'customer_name': stripe_session.customer_details.name,
            'customer_email': stripe_session.customer_details.email,
            'payment_status': stripe_session.payment_status
        }

    except stripe.error.InvalidRequestError:
        return {
            'verified': False,
            'reason': 'Invalid session ID'
        }
    except Exception as e:
        logger.error(f"Error verifying Stripe payment: {str(e)}")
        return {
            'verified': False,
            'reason': 'Error verifying payment'
        }

# Create checkout session (called by checkout.js)
@payment_bp.route("/create-checkout-session", methods=["GET", "POST"])
def create_checkout_session():
    try:
        session = stripe.checkout.Session.create(
            ui_mode="embedded",
            line_items=[
                {
                    # Provide the exact Price ID (for example, pr_1234) of the product you want to sell
                    "price": PRICE_ID,
                    "quantity": 1,
                },
            ],
            mode="payment",
            allow_promotion_codes=False,
            locale="nb",
            return_url=url_for('payment.ordrebekreftelse', _external=True) + "?sessid={CHECKOUT_SESSION_ID}",
            automatic_tax={"enabled": False},
        )
    except Exception as e:
        print(f"Checkout Session Error: {str(e)}")
        return str(e), 500


    return jsonify({
        'clientSecret': session.client_secret,
        'publishableKey': os.getenv('STRIPE_PUBLISHABLE_KEY')
    })

# Render payment confirmation page (after verify_stripe_payment)
@payment_bp.route('/ordrebekreftelse.html', methods=['GET'])
def ordrebekreftelse():
    logger.info("Ordrebekreftelse route accessed")
    try:
        # Get and validate Stripe session ID
        stripe_session_id = request.args.get('sessid')
        if not stripe_session_id:
            logger.warning("Payment verification attempted without Stripe session ID")
            return render_template(
                'ordrebekreftelse.html',
                error_message="Ingen betalingsøkt funnet. Vennligst fullfør kjøpet først.",
                show_success=False
            )

        # Verify payment status
        verification_result = verify_stripe_payment(stripe_session_id)
        logger.info(f"Payment verification result for Stripe session {stripe_session_id}: {verification_result}")

        if verification_result['verified']:
            # Create customer record in database
            success, message = create_customer_record(verification_result, stripe_session_id)

            if not success:
                logger.error(f"Failed to create customer record: {message}")
                return render_template(
                    'ordrebekreftelse.html',
                    error_message="Det oppstod en feil under registrering av betalingen. Vennligst kontakt support.",
                    show_success=False
                )

            # Generate download link using current Flask session
            download_link = generate_download_link(session.get('session_id'))

            return render_template(
                'ordrebekreftelse.html',
                success_message=f"Betaling bekreftet for {verification_result.get('customer_name', 'kunde')}",
                data=verification_result,
                download_link=download_link,
                show_success=True
            )
        else:
            error_message = verification_result.get('reason', 'Ukjent feil ved betalingsverifisering')
            logger.warning(f"Payment verification failed: {error_message}")
            return render_template(
                'ordrebekreftelse.html',
                error_message=error_message,
                show_success=False
            )

    except stripe.error.StripeError as e:
        logger.error(f"Stripe error during payment verification: {str(e)}")
        return render_template(
            'ordrebekreftelse.html',
            error_message="Det oppstod en feil under betalingsverifiseringen. Vennligst prøv igjen senere.",
            show_success=False
        )
    except Exception as e:
        logger.error(f"Error in ordrebekreftelse: {str(e)}", exc_info=True)
        return str(e), 500




