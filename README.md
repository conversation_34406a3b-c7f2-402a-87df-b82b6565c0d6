# Web Application

A sophisticated Flask-based web application featuring a comprehensive CMS, analytics, security, and e-commerce capabilities.

## Features

### Core Functionalities
- **Content Management System (CMS)**
  - Dynamic content creation and management
  - Rich text editing with CKEditor 5 integration
  - Post management and deletion capabilities

- **User Authentication & Security**
  - Flask-Login based authentication system
  - Advanced security measures including:
    - CSRF protection
    - Session management
    - IP-based bot detection
    - Brute force protection
    - Input sanitization

- **Analytics System**
  - Comprehensive visitor tracking
  - Bot detection and filtering
  - Device and browser detection
  - Geographic location tracking
  - Performance monitoring
  - Custom analytics dashboard

- **E-commerce Integration**
  - Payment processing system
  - Checkout session management
  - Digital downloads handling

### Additional Features
- **Localization**
  - Norwegian language interface
  - Norwegian localization in payment processing

- **Social Media Integration**
  - Social sharing features
  - Integration with social media platforms

- **Newsletter Management**
  - Subscription handling
  - Email campaign management

- **Contact Form System**
  - ALTCHA challenge integration
  - Spam protection
  - Email notification system

- **SEO Optimization**
  - Sitemap generation
  - Meta tag management
  - Robot.txt configuration

- **Asset Management**
  - Image resizing and optimization
  - Static asset serving
  - Font management
  - CSS/JS bundling and minification

- **Logging System**
  - Structured JSON logging
  - Rotating file handlers
  - Performance monitoring
  - Security event logging

## Technical Architecture

### Key Architectural Decisions
- **Modular Blueprint Architecture**: Components organized as Flask blueprints for isolation and reusability
- **Vertical Slice Pattern**: Each feature owns its UI, database interactions, and business logic
- **CQRS Implementation**: Separate paths for command (write) and query (read) operations in analytics
- **Event Sourcing**: Critical operations (logins, payments) emit events for audit trails
- **Hexagonal Architecture**: Core business logic isolated from framework/infrastructure

### Backend (Python/Flask)
- **Core Framework**: Flask (v2.3.2) with modular blueprint architecture, leveraging extensions like Flask-SQLAlchemy, Flask-Migrate, Flask-Login, Flask-WTF, Flask-Mail, Flask-Session, Flask-Caching, Flask-Compress, Flask-Limiter, and Flask-Cors.
- **Dependency Injection**: Uses Flask's application context for service location
- **Unit of Work Pattern**: SQLAlchemy session management with atomic transactions
- **Database**: SQLite with production-grade optimizations:
  - WAL mode enabled
  - Memory-mapped I/O
  - Connection pooling
  - Query optimization

### Security Features
- CORS configuration
- Content Security Policy (CSP)
- Ad-hoc SSL/TLS certificate generation for secure connections
- HTTP security headers
- XSS protection
- SQL injection prevention
- Rate limiting
- Bot detection and prevention

### Data Management
- SQLAlchemy ORM
- Flask-Migrate for database migrations
- Session management with Flask-Session
- File-based session storage

### Frontend Integration
- Bootstrap framework via Bootstrap-Flask extension
- CKEditor 5 for rich text editing
- Asset optimization and bundling using Flask-Assets
- Asset optimization and bundling
- Responsive design support

## Project Structure

```
project_root/
├── assets/                 # Static assets management
│   ├── .webassets-cache/  # Cached optimized assets
│   ├── bootstrap/         # Bootstrap v5.3.0 files
│   │   ├── css/          # Compiled CSS files
│   │   ├── js/           # Bootstrap JS components
│   │   └── fonts/        # Bootstrap icons font files
│   ├── ckeditor5/         # CKEditor 5 v39.0.1 resources
│   ├── css/              # Custom SCSS/CSS stylesheets
│   │   └── _components/  # Modular SCSS partials
│   ├── downloads/         # Digital product delivery
│   ├── fonts/            # Custom font files (WOFF2/WOFF)
│   ├── geoip/            # MaxMind GeoLite2 databases
│   ├── img/              # Optimized website images
│   │   ├── cache/        # Resized image variants
│   │   └── original/     # Source image files
│   ├── js/               # Custom JavaScript modules
│   └── optimized/        # Minified production assets
├── instance/              # Instance-specific configs
├── logs/                  # Rotating application logs
├── migrations/            # Database migration scripts
├── modules/               # Core application modules
│   ├── ai.py              # AI-powered content enhancement
│   ├── analytics_collect.py # Data collection system
│   ├── analytics_dashboard.py # Analytics visualization
│   ├── analytics_process.py # Data processing pipelines
│   ├── ckeditor.py        # Rich text editor integration
│   ├── cms.py             # Content management system
│   ├── contact.py         # Contact form handling
│   ├── cookie.py          # Cookie consent management
│   ├── debug.py           # Debugging utilities
│   ├── download.py        # Secure download handling
│   ├── forms.py           # Form definitions
│   ├── imgresize.py       # Image optimization
│   ├── logger.py          # Structured logging system
│   ├── login.py           # Authentication flows
│   ├── mail.py            # Email system integration
│   ├── models.py          # Database models
│   ├── newsletter.py      # Newsletter management
│   ├── optimization.py    # Asset optimization
│   ├── payment.py         # Payment processing
│   ├── routes.py          # Application routing
│   ├── security.py        # Security implementations
│   ├── seo_sitemap.py     # SEO utilities
│   ├── social.py          # Social media integration
│   └── __init__.py        # Application factory
├── Templates/             # Jinja2 HTML templates
├── utilities/             # Development utilities
│   ├── create_user_script.py # User management
│   └── init_db.py         # Database initialization
└── venv/                  # Python virtual environment
```

## Module Details

### AI Integration (ai.py)
- OpenAI GPT-3.5 integration for content enhancement
- Text generation/editing operations:
  - Article writing
  - Proofreading
  - Translation
  - Content continuation
- HTML formatting preservation
- Error handling for API interactions

### Payment Processing (payment.py)
- Stripe payment gateway integration
- Checkout session management
- Order confirmation emails
- Database transaction recording
- Download link generation
- Norwegian localization

### Security Module (security.py)
- CSRF token generation/validation
- ALTCHA challenge system
- IP reputation checks (AbuseIPDB integration)
- Content Security Policy (CSP) configuration
- CORS management
- Input sanitization utilities
- Email validation
- Secure session handling

### Optimization System (optimization.py)
- Asset bundling/minification:
  - CSS/JS concatenation
  - HTML minification
  - Font path correction
- Compression middleware:
  - Gzip/Brotli compression
  - Dynamic content optimization
- Cache control headers
- Performance monitoring

### SEO & Sitemap (seo_sitemap.py)
- Dynamic sitemap generation
- Automatic inclusion of:
  - Static pages
  - Published CMS content
  - Priority-based URL ranking
- XML validation
- Last-modified tracking
- Search engine compliance

### Image Processing (imgresize.py)
- On-demand image resizing
- Format conversion (WebP/AVIF)
- EXIF data stripping
- Cache optimization
- CDN-ready URL generation
- Quality compression settings

### Contact System (contact.py)
- ALTCHA spam protection
- Secure form handling
- Input validation/sanitization
- Email templating
- Rate limiting
- JSON logging

### Newsletter Management (newsletter.py)
- Subscription management
- Double opt-in flows
- Email template system
- Unsubscribe handling
- CSV export capabilities
- GDPR compliance

### Analytics System (analytics_collect.py)
- Real-time visitor tracking
- Device fingerprinting
- Bot detection algorithms
- Geographic mapping
- Performance metrics
- Data aggregation

### Download Management (download.py)
- Secure download links
- Expiration system
- Download counters
- Session validation
- PDF watermarking
- Abuse prevention


## Configuration


## Module Details

### AI Integration (ai.py)
- **Purpose**: Integrates OpenAI's GPT-3.5 model to enhance content creation and editing workflows within the application.
- **Functionality**: Provides features for article writing, proofreading, translation, and content continuation, leveraging the power of AI to assist content creators.
- **Key Features**:
  - Text generation and editing operations via OpenAI API.
  - Preservation of HTML formatting in generated content.
  - Robust error handling for API interactions to ensure application stability.

### Analytics Dashboard Module (analytics_dashboard.py)
- **Purpose**: Provides a visual dashboard for monitoring website traffic and user engagement metrics.
- **Functionality**: Offers a user-friendly interface to visualize key analytics data, aiding in understanding user behavior and website performance.
- **Key Features**:
  - Flask Blueprint to serve the analytics dashboard.
  - Visualizations of website traffic, user behavior, and other relevant metrics.
  - Integration with data collected by the `analytics_collect.py` module.
  - Utilizes charting libraries (e.g., Chart.js) to present data effectively.

### Analytics Processing Module (analytics_process.py)
- **Purpose**: Handles the background processing of raw analytics data to generate meaningful insights and reports.
- **Functionality**: Processes data collected by `analytics_collect.py`, performing aggregations, calculations, and transformations to prepare it for visualization and analysis.
- **Key Features**:
  - Background task processing for efficient data handling.
  - Data aggregation and report generation for various metrics.
  - Scheduled tasks for periodic analytics updates to ensure data freshness.
  - May use task queues (e.g., Celery) for asynchronous processing.

### CKEditor Integration (ckeditor.py)
- **Purpose**: Integrates the CKEditor 5 rich text editor into the Flask application, providing advanced text editing capabilities for CMS content.
- **Functionality**: Manages the integration of CKEditor 5, handling file uploads, configurations, and custom plugins to enhance content creation within the CMS.
- **Key Features**:
  - Flask Blueprint for CKEditor 5 integration.
  - Handles file uploads from CKEditor to the server, with configurable storage.
  - Provides necessary configurations and potentially custom plugins for CKEditor 5.
  - Enables rich text editing for CMS content, enhancing content authoring experience.

### Content Management System (cms.py)
- **Purpose**: Implements the core Content Management System (CMS) functionalities of the application, allowing for dynamic content creation, management, and delivery.
- **Functionality**: Provides the backend logic and routes for managing website content, including posts, pages, and potentially other content types.
- **Key Features**:
  - Flask Blueprint for CMS functionalities.
  - Defines routes and logic for content creation, editing, and deletion.
  - Manages content lifecycle, including drafts, published posts, and content listings.
  - Integrates with database models to store and retrieve content.

### Cookie Module (cookie.py)
- **Purpose**: Manages cookie consent and GDPR compliance related to cookie usage on the website.
- **Functionality**: Implements features for obtaining and managing user consent for cookies, ensuring compliance with privacy regulations like GDPR.
- **Key Features**:
  - Flask Blueprint for handling cookie consent.
  - Implements a cookie consent banner to inform users about cookie usage.
  - Manages user preferences for cookie consent, storing and retrieving consent choices.
  - Ensures GDPR compliance by providing users control over cookies.

### Debug Module (debug.py)
- **Purpose**: Provides debugging and development utilities to aid in the development process.
- **Functionality**: Offers various tools and routes to assist developers in debugging and testing the application, which should be disabled or secured in production environments.
- **Key Features**:
  - Flask Blueprint for debug utilities.
  - Provides debugging routes and tools for development purposes.
  - Includes features like debug logs, profiling tools, or test routes.
  - **Important**: Should be disabled or secured in production to prevent exposing sensitive information.

### Forms Module (forms.py)
- **Purpose**: Defines Flask-WTF form classes used throughout the application for handling user input and data validation.
- **Functionality**: Centralizes the definition of forms, promoting reusability and maintainability of form structures across different parts of the application.
- **Key Features**:
  - Defines Flask-WTF form classes for various application features.
  - Reusable form definitions for contact forms, newsletter subscriptions, user registration, etc.
  - Includes form fields, validators, and CSRF protection.
  - Simplifies form handling in Flask views and templates.

### Logger Module (logger.py)
- **Purpose**: Configures and initializes the structured logging system for the application, providing robust logging capabilities for monitoring and debugging.
- **Functionality**: Sets up logging configurations, formatters, and handlers to generate structured JSON logs, which are easier to parse and analyze.
- **Key Features**:
  - Configures structured logging using Python's `logging` module.
  - Uses JSON logging format for logs, enhancing machine readability.
  - Integrates with rotating file handlers for efficient log file management.
  - Provides different log levels (e.g., INFO, WARNING, ERROR) for various event severities.

### Login Module (login.py)
- **Purpose**: Implements user authentication and session management functionalities for the application.
- **Functionality**: Handles user login, logout, registration, and password management, securing access to protected parts of the application.
- **Key Features**:
  - Flask Blueprint for user authentication routes.
  - Defines routes for login, logout, and user registration.
  - Uses Flask-Login for secure session management and user authentication.
  - May include features like password reset and account management.

### Mail Module (mail.py)
- **Purpose**: Initializes and configures Flask-Mail, providing email sending capabilities for various application features like contact forms, newsletters, and transactional emails.
- **Functionality**: Sets up the email system, allowing the application to send emails for notifications, confirmations, and marketing purposes.
- **Key Features**:
  - Initializes and configures Flask-Mail extension.
  - Provides utilities for sending emails with attachments and templates.
  - Used for sending emails from contact forms, newsletter subscriptions, payment confirmations, etc.
  - Integrates with email templates for consistent email design.

### Models Module (models.py)
- **Purpose**: Defines SQLAlchemy database models that represent the application's data structure and interact with the database.
- **Functionality**: Specifies the structure of database tables, relationships between tables, and provides an Object-Relational Mapper (ORM) interface to interact with the database.
- **Key Features**:
  - Defines SQLAlchemy database models for various entities (e.g., User, Post, Product, Order).
  - Represents the application's data structure in Python classes.
  - Includes model definitions, relationships, and potentially model-specific methods.
  - Used by other modules to interact with the database.

### Routes Module (routes.py)
- **Purpose**: Defines the main application routes and URL mappings, connecting user requests to the appropriate view functions and application logic.
- **Functionality**: Handles URL routing for the primary functionalities of the web application, directing traffic and managing user interactions.
- **Key Features**:
  - Defines main application routes using Flask's routing system.
  - Handles routes for the index page and other general application views.
  - Connects frontend templates with backend logic through view functions.
  - May organize routes using Flask Blueprints for modularity.

### Social Module (social.py)
- **Purpose**: Implements social media integration features, allowing users to share content on social platforms and potentially integrate with social media APIs.
- **Functionality**: Provides features for social sharing, embedding social content, and potentially interacting with social media platforms for marketing or engagement purposes.
- **Key Features**:
  - Flask Blueprint for social media integrations.
  - Implements social sharing features, allowing users to share content on platforms like Facebook, Twitter, etc.
  - May include integrations with social media APIs for fetching content or user data.
  - Enhances the application's social presence and user engagement.

The application uses environment variables for configuration. Key settings include:
**General Settings:**
- `FLASK_ENV`: Application environment (development/production)
- `SECRET_KEY`: Flask application secret key
The application uses environment variables for configuration. Key settings include:

**General Settings:**
- `FLASK_ENV`: Application environment (development/production)
- `SECRET_KEY`: Flask application secret key

**Database Settings:**
- `DATABASE_URL`: Database connection string

**Email Settings:**
- `MAIL_*`: Configuration for email server (e.g., `MAIL_SERVER`, `MAIL_PORT`, etc.)

**Session Settings:**
- `SESSION_*`: Flask-Session configuration (e.g., `SESSION_TYPE`, `SESSION_REDIS`, etc.)

**Payment Gateway Settings:**
- `STRIPE_*`: Stripe API keys and settings

**AI Integration Settings:**
- `OPENAI_API_KEY`: API key for OpenAI GPT-3.5 integration

## Development Setup

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # or venv\Scripts\activate on Windows
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configurations
   ```

4. Initialize the database:
   ```bash
   flask db upgrade
   ```

5. Run the development server:
   ```bash
   flask run
   ```

## Security Considerations
- Regular security audits recommended
- Monitor AbuseIPDB integration
- Rotate API keys periodically
- Keep dependencies updated
- Use HTTPS in production
