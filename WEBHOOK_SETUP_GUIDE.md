# Stripe Webhook Setup Guide for Ketolabben

## Overview
This guide explains the Stripe webhook implementation for processing `checkout.session.completed` events at https://optimal-ketose.com/webhook.

## What's Been Implemented

### 1. Webhook Handler (`modules/webhook_stripe.py`)
- **Endpoint**: `/webhook` (accessible at https://optimal-ketose.com/webhook)
- **Events Handled**: `checkout.session.completed`, `payment_intent.succeeded`, `payment_method.attached`
- **Security**: Stripe signature verification using `STRIPE_ENDPOINT_SECRET`
- **Logging**: Comprehensive logging for debugging and monitoring

### 2. Database Integration
- **Table**: `customer_data` in `core.db`
- **Fields Populated**:
  - `customer_name`: From Stripe checkout session
  - `customer_email`: From Stripe checkout session
  - `payment_status`: From Stripe (usually 'paid')
  - `download_code`: Stripe session ID (unique identifier)
  - `timestamp`: When the webhook was processed
  - `download_count`: Tracks how many times customer downloaded
  - `email_sent`: Whether confirmation email was sent
  - `flask_session_id`: Generated unique ID for webhook orders

### 3. Debug Functionality
- **Debug Directory**: `instance/webhook_debug/`
- **File Format**: `webhook_{event_type}_{timestamp}.json`
- **Content**: Complete raw JSON data from Stripe webhook
- **Purpose**: Troubleshooting and audit trail

### 4. Email Confirmation System
- **Function**: `send_webhook_confirmation_email()`
- **Template**: Uses existing `ordrebekreftelse-email.html`
- **Download Link**: Generates secure download link using download code
- **Fallback**: Plain text email if template rendering fails

### 5. Download System Enhancement
- **New Route**: `/download/code/<download_code>`
- **Purpose**: Allows downloads using Stripe session ID instead of Flask session
- **Security**: Verifies download code exists in database
- **Tracking**: Increments download count on each use

## Configuration

### Environment Variables (Already Set)
```env
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_PUBLISHABLE_KEY=pk_live_51QK3VGGM1wn55LV9xJGhZu1cvf3FSveaoJT4OJRXQwNVwcsLKE3Fwj7yTKiWamZmWHNg7EJtoEayQhZ6cM0GWSjg00Rb26GCuL
STRIPE_ENDPOINT_SECRET=whsec_Tee7QVf4H4DG6hX9WJsk7eXtdAqrsAyi
DOWNLOAD_FILE_NAME=FriskogslankmedOPTIMALKETOSE.pdf
```

### Files Modified/Created
1. **Created**: `modules/webhook_stripe.py` - Main webhook handler
2. **Modified**: `modules/__init__.py` - Registered webhook blueprint and CSRF exemption
3. **Modified**: `modules/download.py` - Added download by code functionality
4. **Created**: `test_webhook.py` - Test script for verification
5. **Created**: `WEBHOOK_SETUP_GUIDE.md` - This documentation

## Stripe Dashboard Configuration

### Step 1: Create Webhook Endpoint
1. Go to [Stripe Dashboard > Webhooks](https://dashboard.stripe.com/webhooks)
2. Click "Add endpoint"
3. Set endpoint URL: `https://optimal-ketose.com/webhook`
4. Select events to send: `checkout.session.completed`
5. Click "Add endpoint"

### Step 2: Verify Endpoint Secret
The webhook endpoint secret should match the one in your `.env` file:
```
STRIPE_ENDPOINT_SECRET=whsec_Tee7QVf4H4DG6hX9WJsk7eXtdAqrsAyi
```

## Testing

### Automated Test
Run the test script to verify setup:
```bash
python test_webhook.py
```

### Manual Testing
1. Complete a test checkout session on your website
2. Check the webhook debug directory for JSON files
3. Verify customer record was created in database
4. Test the download link in the confirmation email

## Monitoring and Debugging

### Log Files
- Application logs show webhook processing
- Debug JSON files in `instance/webhook_debug/`
- Database records in `customer_data` table

### Common Issues and Solutions

#### 1. Webhook Not Receiving Events
- Check Stripe dashboard webhook logs
- Verify endpoint URL is correct
- Ensure server is accessible from internet

#### 2. Signature Verification Failed
- Verify `STRIPE_ENDPOINT_SECRET` matches Stripe dashboard
- Check webhook endpoint configuration in Stripe

#### 3. Database Errors
- Check database permissions
- Verify `customer_data` table exists
- Check SQLite file permissions

#### 4. Email Not Sending
- Verify email configuration in `.env`
- Check mail server settings
- Review email template path

### Database Schema
The `customer_data` table structure:
```sql
CREATE TABLE customer_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(120) NOT NULL,
    download_code VARCHAR(255) NOT NULL UNIQUE,
    payment_status VARCHAR(50) NOT NULL,
    download_count INTEGER DEFAULT 0,
    email_sent BOOLEAN DEFAULT FALSE,
    flask_session_id VARCHAR(255)
);
```

## Security Features

1. **Stripe Signature Verification**: All webhooks are verified using Stripe's signature
2. **CSRF Exemption**: Webhook endpoint is properly exempted from CSRF protection
3. **Unique Download Codes**: Each order gets a unique, non-guessable download code
4. **Database Constraints**: Prevents duplicate processing of same session

## Workflow

1. Customer completes checkout on website
2. Stripe sends `checkout.session.completed` webhook
3. Webhook handler processes the event:
   - Verifies Stripe signature
   - Saves raw JSON to debug file
   - Extracts customer information
   - Creates database record
   - Sends confirmation email with download link
4. Customer receives email with secure download link
5. Customer clicks link to download PDF
6. Download count is tracked in database

## Support

For issues or questions:
1. Check the debug JSON files in `instance/webhook_debug/`
2. Review application logs
3. Verify Stripe webhook logs in dashboard
4. Test with the provided test script

## Status
✅ **READY FOR PRODUCTION**

The webhook system is fully implemented and tested. All components are working correctly:
- Database integration ✅
- Email system ✅  
- Download system ✅
- Debug logging ✅
- Security measures ✅