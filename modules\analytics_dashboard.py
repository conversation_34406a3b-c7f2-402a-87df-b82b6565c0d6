from flask import Blueprint, render_template, jsonify, request, url_for, current_app
from datetime import datetime, timedelta
from .analytics_process import get_analytics_data
from .analytics_collect import requires_auth
import os
from sqlalchemy import func
from .models import db, Analytics
import traceback

analytics_dashboard_bp = Blueprint(
    'analytics_dashboard', 
    __name__, 
    template_folder=os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'Templates')),
    static_folder=os.path.join(os.path.dirname(__file__), 'static'),
    static_url_path='/analytics/static'
)

def parse_date(date_str):
    """
    Parse date string to datetime object
    
    Args:
        date_str (str): Date string in YYYY-MM-DD format
    
    Returns:
        datetime: Parsed datetime object
    """
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        current_app.logger.error(f"Invalid date format: {date_str}")
        return None

def generate_time_series_data(start_date, end_date):
    """
    Generate time series data for pageviews and unique visitors
    
    Args:
        start_date (datetime): Start date for time series
        end_date (datetime): End date for time series
    
    Returns:
        dict: Time series data for pageviews and unique visitors
    """
    try:
        current_app.logger.info(f"Generating time series data from {start_date} to {end_date}")
        
        # Adjust start and end dates to include full first and last days
        start_date_inclusive = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date_inclusive = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Pageviews over time
        pageviews_query = db.session.query(
            func.date(Analytics.timestamp).label('date'),
            func.count(Analytics.id).label('pageviews')
        ).filter(
            Analytics.is_bot == False,
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by(func.date(Analytics.timestamp)).order_by('date').all()
        
        current_app.logger.info(f"Pageviews query result: {pageviews_query}")
        
        # Unique visitors over time
        unique_visitors_query = db.session.query(
            func.date(Analytics.timestamp).label('date'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            Analytics.is_bot == False,
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by(func.date(Analytics.timestamp)).order_by('date').all()
        
        current_app.logger.info(f"Unique visitors query result: {unique_visitors_query}")
        
        # Format data for chart.js
        pageviews_data = {
            'labels': [str(row.date) for row in pageviews_query],
            'data': [row.pageviews for row in pageviews_query]
        }
        
        unique_visitors_data = {
            'labels': [str(row.date) for row in unique_visitors_query],
            'data': [row.unique_visitors for row in unique_visitors_query]
        }
        
        current_app.logger.info(f"Pageviews data: {pageviews_data}")
        current_app.logger.info(f"Unique visitors data: {unique_visitors_data}")
        
        return {
            'pageviews_over_time': pageviews_data,
            'unique_visitors_over_time': unique_visitors_data
        }
    except Exception as e:
        current_app.logger.error(f"Error generating time series data: {e}")
        current_app.logger.error(traceback.format_exc())
        return {
            'pageviews_over_time': {'labels': [], 'data': []},
            'unique_visitors_over_time': {'labels': [], 'data': []}
        }

def calculate_weekly_traffic_distribution(start_date, end_date):
    """
    Calculate traffic distribution across days of the week
    
    Args:
        start_date (datetime): Start date for analysis
        end_date (datetime): End date for analysis
    
    Returns:
        list: Traffic count for each day of the week [Mon, Tue, Wed, Thu, Fri, Sat, Sun]
    """
    try:
        # Query to count visitors per day of the week
        weekly_traffic_query = db.session.query(
            func.extract('dow', Analytics.timestamp).label('day_of_week'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            Analytics.is_bot == False,
            Analytics.timestamp.between(start_date, end_date)
        ).group_by('day_of_week').order_by('day_of_week').all()
        
        # Initialize list for 7 days of the week (Sunday = 0, Monday = 1, etc.)
        weekly_traffic = [0] * 7
        
        # Populate the list with actual counts
        for row in weekly_traffic_query:
            # Adjust day index to match Chart.js (Sunday = 6, Monday = 0)
            day_index = (int(row.day_of_week) + 6) % 7
            weekly_traffic[day_index] = row.unique_visitors
        
        current_app.logger.info(f"Weekly traffic distribution: {weekly_traffic}")
        return weekly_traffic
    
    except Exception as e:
        current_app.logger.error(f"Error calculating weekly traffic distribution: {e}")
        current_app.logger.error(traceback.format_exc())
        return [0] * 7

def calculate_hourly_traffic_distribution(start_date, end_date):
    """
    Calculate traffic distribution across hours of the day
    
    Args:
        start_date (datetime): Start date for analysis
        end_date (datetime): End date for analysis
    
    Returns:
        list: Pageview count for each hour of the day [0-23]
    """
    try:
        # Query to count pageviews per hour
        hourly_traffic_query = db.session.query(
            func.extract('hour', Analytics.timestamp).label('hour_of_day'),
            func.count(Analytics.id).label('pageviews')
        ).filter(
            Analytics.is_bot == False,
            Analytics.timestamp.between(start_date, end_date)
        ).group_by('hour_of_day').order_by('hour_of_day').all()
        
        # Initialize list for 24 hours of the day
        hourly_traffic = [0] * 24
        
        # Populate the list with actual counts
        for row in hourly_traffic_query:
            hour_index = int(row.hour_of_day)
            hourly_traffic[hour_index] = row.pageviews
        
        current_app.logger.info(f"Hourly traffic distribution: {hourly_traffic}")
        return hourly_traffic
    
    except Exception as e:
        current_app.logger.error(f"Error calculating hourly traffic distribution: {e}")
        current_app.logger.error(traceback.format_exc())
        return [0] * 24

@analytics_dashboard_bp.route('/analytics.html')
@requires_auth
def analytics_dashboard():
    """Render the analytics dashboard page with optional date range"""
    # Get start and end dates from query parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # If no dates provided, default to last 30 days
    if not (start_date and end_date):
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        start_date = start_date.strftime('%Y-%m-%d')
        end_date = end_date.strftime('%Y-%m-%d')
    
    # Validate date format (optional: add more robust validation if needed)
    try:
        # Attempt to parse dates to ensure they are valid
        datetime.strptime(start_date, '%Y-%m-%d')
        datetime.strptime(end_date, '%Y-%m-%d')
    except ValueError:
        # If dates are invalid, fall back to default
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        start_date = start_date.strftime('%Y-%m-%d')
        end_date = end_date.strftime('%Y-%m-%d')
    
    # Get initial data
    data = get_analytics_data(start_date, end_date)
    
    return render_template(
        'cms-analytics-dashboard.html',
        start_date=start_date,
        end_date=end_date,
        initial_data=data  # Pass initial data to template
    )

@analytics_dashboard_bp.route('/analytics.html/data')
@requires_auth
def get_dashboard_data():
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        current_app.logger.info(f"Dashboard data request - Start Date: {start_date}, End Date: {end_date}")
        
        if not start_date or not end_date:
            current_app.logger.warning("Missing required parameters: start_date and end_date")
            return jsonify({
                'error': 'Missing required parameters: start_date and end_date'
            }), 400
        
        # Parse dates
        start = parse_date(start_date)
        end = parse_date(end_date)
        
        current_app.logger.info(f"Parsed dates - Start: {start}, End: {end}")
        
        # Get main analytics data
        data = get_analytics_data(start_date, end_date)
        if isinstance(data, tuple):  # Error case
            return jsonify(data[0]), data[1]  # Return error response with status code
            
        # Add time series data
        time_series_data = generate_time_series_data(start, end)
        data['time_series'] = time_series_data
        
        # Add weekly traffic distribution
        data['weekly_traffic_distribution'] = calculate_weekly_traffic_distribution(start, end)
        
        # Add hourly traffic distribution
        data['hourly_traffic_distribution'] = calculate_hourly_traffic_distribution(start, end)
        
        # Add average visit duration
        data['avg_visit_duration'] = data.get('avg_visit_duration', 0)
        
        current_app.logger.info(f"Returning dashboard data: {data}")
        
        return jsonify(data)
        
    except Exception as e:
        current_app.logger.error(f"Failed to fetch analytics data: {e}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'error': 'Failed to fetch analytics data',
            'details': str(e)
        }), 500
