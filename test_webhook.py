#!/usr/bin/env python3
"""
Test script to verify webhook setup
"""
import os
import sys
import json
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules import create_app
from modules.models import db, CustomerData

def test_webhook_setup():
    """Test the webhook setup"""
    print("Testing webhook setup...")
    
    # Create app context
    app = create_app()
    
    with app.app_context():
        # Test database connection
        try:
            # Check if customer_data table exists and is accessible
            count = CustomerData.query.count()
            print(f"✓ Database connection successful. Found {count} customer records.")
        except Exception as e:
            print(f"✗ Database error: {str(e)}")
            return False
        
        # Test environment variables
        required_env_vars = [
            'STRIPE_SECRET_KEY',
            'STRIPE_ENDPOINT_SECRET',
            'DOWNLOAD_FILE_NAME'
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"✗ Missing environment variables: {', '.join(missing_vars)}")
            return False
        else:
            print("✓ All required environment variables are set.")
        
        # Test webhook debug directory creation
        try:
            debug_dir = os.path.join(os.path.dirname(__file__), 'instance', 'webhook_debug')
            os.makedirs(debug_dir, exist_ok=True)
            print(f"✓ Webhook debug directory ready: {debug_dir}")
        except Exception as e:
            print(f"✗ Could not create webhook debug directory: {str(e)}")
            return False
        
        # Test download file exists
        download_file = os.path.join(os.path.dirname(__file__), 'assets', 'downloads', os.getenv('DOWNLOAD_FILE_NAME'))
        if os.path.exists(download_file):
            print(f"✓ Download file exists: {download_file}")
        else:
            print(f"⚠ Download file not found: {download_file}")
            print("  Make sure to place your PDF file in the assets/downloads/ directory")
        
        print("\n" + "="*50)
        print("WEBHOOK SETUP SUMMARY")
        print("="*50)
        print(f"Webhook URL: https://optimal-ketose.com/webhook")
        print(f"Event to listen for: checkout.session.completed")
        print(f"Debug files will be saved to: {debug_dir}")
        print(f"Customer data will be stored in: customer_data table")
        print("="*50)
        
        return True

def create_test_customer_record():
    """Create a test customer record to verify database functionality"""
    app = create_app()
    
    with app.app_context():
        try:
            # Create a test record
            test_customer = CustomerData(
                customer_name="Test Customer",
                customer_email="<EMAIL>",
                payment_status="paid",
                download_code="test_session_123",
                timestamp=datetime.now(),
                download_count=0,
                email_sent=False,
                flask_session_id="test_flask_session_123"
            )
            
            db.session.add(test_customer)
            db.session.commit()
            
            print("✓ Test customer record created successfully")
            
            # Clean up - remove the test record
            db.session.delete(test_customer)
            db.session.commit()
            
            print("✓ Test customer record cleaned up")
            return True
            
        except Exception as e:
            print(f"✗ Error creating test customer record: {str(e)}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("Ketolabben Stripe Webhook Test")
    print("=" * 40)
    
    if test_webhook_setup():
        print("\n✓ Basic webhook setup test passed!")
        
        if create_test_customer_record():
            print("✓ Database functionality test passed!")
            print("\n🎉 Webhook is ready to use!")
            print("\nNext steps:")
            print("1. Configure your Stripe webhook endpoint to point to: https://optimal-ketose.com/webhook")
            print("2. Set the webhook to listen for 'checkout.session.completed' events")
            print("3. Test with a real Stripe checkout session")
        else:
            print("\n⚠ Database test failed. Check your database configuration.")
    else:
        print("\n✗ Webhook setup test failed. Please fix the issues above.")