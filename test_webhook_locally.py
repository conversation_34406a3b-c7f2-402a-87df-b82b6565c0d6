#!/usr/bin/env python3
"""
Test webhook functionality locally
"""
import os
import sys
import json
import requests
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_local_webhook():
    """Test the webhook endpoint locally"""
    print("Testing webhook endpoint locally...")
    
    # Test data that mimics a Stripe checkout.session.completed event
    test_webhook_data = {
        "id": "evt_test_webhook",
        "object": "event",
        "api_version": "2020-08-27",
        "created": int(datetime.now().timestamp()),
        "data": {
            "object": {
                "id": "cs_test_session_123",
                "object": "checkout.session",
                "payment_status": "paid",
                "customer_details": {
                    "email": "<EMAIL>",
                    "name": "Test Customer"
                }
            }
        },
        "livemode": False,
        "pending_webhooks": 1,
        "request": {
            "id": None,
            "idempotency_key": None
        },
        "type": "checkout.session.completed"
    }
    
    try:
        # Test local webhook endpoint
        response = requests.post(
            'http://localhost:8282/webhook',
            json=test_webhook_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("✓ Local webhook endpoint is working!")
            return True
        else:
            print(f"✗ Local webhook returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to local server. Is it running on port 8282?")
        return False
    except Exception as e:
        print(f"✗ Error testing webhook: {str(e)}")
        return False

def test_download_routes():
    """Test download routes locally"""
    print("\nTesting download routes...")
    
    try:
        # Test download by code route (should return 404 for non-existent code)
        response = requests.get('http://localhost:8282/download/code/test_code_123', timeout=10)
        print(f"Download by code test - Status: {response.status_code}")
        
        if response.status_code == 404:
            print("✓ Download by code route is working (correctly returns 404 for invalid code)")
        else:
            print(f"? Download by code returned unexpected status: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to local server for download test")
        return False
    except Exception as e:
        print(f"✗ Error testing download routes: {str(e)}")
        return False

if __name__ == "__main__":
    print("Local Webhook and Download Test")
    print("=" * 40)
    
    webhook_ok = test_local_webhook()
    download_ok = test_download_routes()
    
    if webhook_ok and download_ok:
        print("\n✓ All local tests passed!")
        print("\nThe webhook code is working locally.")
        print("Next step: Deploy to production server.")
    else:
        print("\n⚠ Some tests failed. Check the local server setup.")