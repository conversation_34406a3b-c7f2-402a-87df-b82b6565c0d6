from flask import Flask, render_template_string, Response
from modules.structured_data import inject_structured_data, generate_organization_schema, generate_website_schema
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Create a test app
app = Flask(__name__)

@app.route('/')
def test_page():
    """Test page for structured data injection"""
    # Create a simple HTML template
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Structured Data Test</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>Structured Data Test</h1>
        <p>This page tests the injection of structured data.</p>
        <p>View the page source to see the structured data.</p>
    </body>
    </html>
    """
    
    # Generate structured data
    structured_data = [
        generate_organization_schema(),
        generate_website_schema()
    ]
    
    # Inject structured data
    processed = inject_structured_data(html, structured_data)
    
    # Log the processed HTML for debugging
    logger.debug("Processed HTML:")
    logger.debug(processed)
    
    # Check if structured data was injected
    if '<script type="application/ld+json">' in processed:
        logger.info("Structured data was successfully injected.")
    else:
        logger.warning("Structured data was NOT injected.")
    
    # Return the processed HTML with explicit content type
    return Response(processed, mimetype='text/html')

@app.route('/template')
def template_test():
    """Test page using Flask's template rendering"""
    # Create a simple HTML template
    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Template Test</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>Template Test</h1>
        <p>This page tests structured data with Flask templates.</p>
    </body>
    </html>
    """
    
    # Render the template
    return render_template_string(template)

@app.route('/raw')
def raw_html():
    """Test page with raw HTML"""
    # Create a simple HTML
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Raw HTML Test</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>Raw HTML Test</h1>
        <p>This page tests structured data with raw HTML.</p>
    </body>
    </html>
    """
    
    # Return raw HTML
    return html

if __name__ == '__main__':
    print("Starting test server at http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)