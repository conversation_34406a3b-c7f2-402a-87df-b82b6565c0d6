# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION BEGIN
PassengerAppRoot "/home/<USER>/public_html/optimal-ketose.com"
PassengerBaseURI "/"
PassengerPython "/home/<USER>/virtualenv/public_html/optimal-ketose.com/3.12/bin/python"
# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION END
# DO NOT REMOVE OR MODIFY. CLOUDLINUX ENV VARS CONFIGURATION BEGIN
<IfModule Litespeed>
</IfModule>
# DO NOT REMOVE OR MODIFY. CLOUDLINUX ENV VARS CONFIGURATION END

# Enable mod_rewrite
<IfModule mod_rewrite.c>
RewriteEngine On

# Redirect from HTTP to HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirect from www to non-www
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^ https://%1%{REQUEST_URI} [L,R=301]

# Redirect index.html to directory root
RewriteCond %{THE_REQUEST} /index\.html [NC]
RewriteRule ^(.*/)?index\.html$ /$1 [R=301,L]
</IfModule>

# Static page redirects
Redirect 301 /produkt/optimal-ketose-e-bok/ /kostplan.html
Redirect 301 /keto-diett-kostplan/ /kostplan.html
Redirect 301 /facebook-omtaler/ /omtaler.html
Redirect 301 /category/oppskrifter/ /cms-index.html?filter=oppskrift
Redirect 301 /category/lavkarbo-keto-oppskrifter/ /cms-index.html?filter=oppskrift
Redirect 301 /oppskrift/ /cms-index.html?filter=oppskrift
Redirect 301 /oppskrift/innbakt-lavkarbo-stromboli/ /innbakt-lavkarbo-stromboli
Redirect 301 /newsletter/ /nyhetsbrev.html
Redirect 301 /kontakt-oss/ /kontakt.html
Redirect 301 /om-oss/ /om.html
Redirect 301 /personvernerklaering/ /gdpr.html
Redirect 301 /vilkar/ /vilkar.html

# Blog / Info articles
Redirect 301 /optimal-ketose/ /om-optimal-ketose
Redirect 301 /leptin-resistent/ /leptin-resistent-overvektig-selv-om-du-ikke-overspiser
Redirect 301 /hva-er-ketose/ /hva-er-ketose
Redirect 301 /forskjell-ketose-og-optimal-ketose/ /forskjellen-pa-ketose-og-optimal-ketose
Redirect 301 /kourtney-kardashians-nye-livsstil-lavkarbo/ /kortney-kardashian-avslorer-sin-nye-livsstil
Redirect 301 /spennende-film-pa-netflix-forsvarer-ketogen-kost-med-periodisk-faste/ /spennende-film-pa-amazon-prime-forsvarer-ketogen-kost-med-periodisk-faste
Redirect 301 /ketosticks/ /kan-man-sjekke-optimal-ketose-med-ketostix-eller-blodsukkerapparat
Redirect 301 /ti-tips-for-lavkarbo-middag/ /ti-tips-til-enkel-lavkarbo-middag
Redirect 301 /periodisk-faste/ /periodisk-faste
Redirect 301 /ketose-forklart-pa-90-sekunder-video/ /ketose-forklart-pa-90-sekunder-video

# Recipes
Redirect 301 /lavkarbo-loff-pa-3-minutter/ /lavkarbo-loff-pa-3-minutter
Redirect 301 /lavkarbo-chocolate-chip-cookies/ /lavkarbo-chocolate-chip-cookies
Redirect 301 /bringebaer-chia-fresca/ /bringebaer-chia-fresca
Redirect 301 /bakt-egg-i-avokado/ /bakt-egg-i-avokado
Redirect 301 /immunstyrkende-juice/ /immunstyrkende-juice
Redirect 301 /innbakt-lavkarbo-stromboli/ /innbakt-lavkarbo-stromboli
Redirect 301 /kjottsuppe/ /kjottsuppe
Redirect 301 /fluffy-aquafaba-sjokolademousse/ /fluffy-aquafaba-sjokolademousse
Redirect 301 /kyllingsalat/ /kyllingsalat
Redirect 301 /keto-oreo-dream-cake/ /keto-oreo-dream-cake
Redirect 301 /dronning-mauds-lavkarbo-fromasj/ /dronning-mauds-lavkarbo-fromasj
Redirect 301 /spro-osteboller/ /spro-osteboller

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php81” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php81 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
