from datetime import datetime, timedelta
from sqlalchemy import func, text, case, desc
from flask import Response, jsonify, current_app, request
import logging
from urllib.parse import urlparse, urlunparse, parse_qs
import user_agents
import csv
from io import StringIO
from sqlalchemy.exc import SQLAlchemyError
import traceback
from .models import db, Analytics
from .analytics_collect import parse_date, strip_url_parameters, extract_domain, extract_os

# Configure logging
logger = logging.getLogger(__name__)

def parse_referrer(referrer):
    """Parse referrer URL to extract UTM parameters and other information"""
    if not referrer:
        return {'source': 'direct'}

    try:
        parsed = urlparse(referrer)
        query_params = parse_qs(parsed.query)

        result = {
            'domain': parsed.netloc.lower() if parsed.netloc else 'direct',
            'path': parsed.path,
            'utm_source': query_params.get('utm_source', [None])[0],
            'utm_medium': query_params.get('utm_medium', [None])[0],
            'utm_campaign': query_params.get('utm_campaign', [None])[0],
            'utm_term': query_params.get('utm_term', [None])[0],
            'utm_content': query_params.get('utm_content', [None])[0]
        }

        # Handle common social media referrers
        domain = result['domain']
        if any(social in domain for social in ['facebook', 'twitter', 'linkedin', 'instagram']):
            result['source_type'] = 'social'
        elif any(search in domain for search in ['google', 'bing', 'yahoo', 'duckduckgo']):
            result['source_type'] = 'search'
        else:
            result['source_type'] = 'referral'

        return result
    except Exception as e:
        current_app.logger.warning(f"Failed to parse referrer: {referrer}, Error: {e}")
        return {'source': 'invalid'}

def normalize_browser(browser_string):
    """
    Normalize browser name by removing version numbers and standardizing names

    Args:
        browser_string (str): Full browser name with version

    Returns:
        str: Normalized browser name without version
    """
    if not browser_string:
        return 'Unknown'

    # Common browser names to normalize
    browser_mapping = {
        'Edge': ['edge', 'microsoft edge', 'edg', 'edga', 'edgios'],
        'Opera': ['opera', 'opera browser', 'opr'],
        'Internet Explorer': ['internet explorer', 'ie', 'msie'],
        'CC Cleaner': ['ccleaner'],
        'Brave': ['brave', 'brave browser'],
        'Vivaldi': ['vivaldi', 'vivaldi browser'],
        'Yandex': ['yandex', 'yandex browser'],
        'Samsung': ['samsung'],
        'UC Browser': ['uc browser', 'ucbrowser'],
        'DuckDuckGo': ['duckduckgo'],
        'Firefox': ['firefox', 'mozilla firefox', 'ff'],
        'Instagram': ['instagram', 'instagram browser'],
        'Chrome': ['chrome', 'chromium', 'google chrome'],
        'Safari': ['safari', 'apple safari'],
        'Cpanel HTTP Client': ['cpanel-http-client'],
        'Chrome Prefex Proxy': ['Chrome Privacy Preserving Prefetch Proxy'],
        'Anthill': ['anthill']
    }

    # Lowercase the browser string for matching
    lower_browser = browser_string.lower()

    # Check for exact or partial matches in browser mapping
    for normalized_name, aliases in browser_mapping.items():
        if any(alias in lower_browser for alias in aliases):
            return normalized_name

    # If no match found, return the first word of the browser string
    return browser_string.split()[0]

def get_analytics_data(start_date=None, end_date=None):
    """
    Retrieve comprehensive analytics data with robust error handling

    Args:
        start_date (str, optional): Start date for filtering
        end_date (str, optional): End date for filtering

    Returns:
        dict: Comprehensive analytics data
    """
    try:
        # Parse dates
        start = parse_date(start_date) if start_date else datetime.now() - timedelta(days=30)
        end = parse_date(end_date) if end_date else datetime.now()

        if not start or not end:
            return {'error': 'Invalid date format'}, 400

        # Adjust start and end dates to include full first and last days
        start_date_inclusive = start.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date_inclusive = end.replace(hour=23, minute=59, second=59, microsecond=999999)

        # Get total visits (excluding bots)
        total_visits = Analytics.query.filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).count()

        # Get unique visitors count using subquery to ensure we count distinct sessions
        unique_sessions_subquery = db.session.query(
            Analytics.sessionid
        ).distinct().filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).subquery()

        total_unique_visitors = db.session.query(
            func.count(unique_sessions_subquery.c.sessionid)
        ).scalar()

        current_app.logger.info(f"Total unique visitors (using subquery): {total_unique_visitors}")

        # Get total unique visitors across all days
        current_app.logger.info(f"Unique visitors: {total_unique_visitors}")

        # Calculate pages per session
        pages_per_session = round(float(total_visits) / float(total_unique_visitors), 2) if total_unique_visitors > 0 else 0
        current_app.logger.info(f"Pages per session: {pages_per_session} (total_visits: {total_visits}, total_unique_visitors: {total_unique_visitors})")

        if total_visits == 0:
            return {
                'overview': {
                    'total_pageviews': 0,
                    'unique_visitors': 0,
                    'pageviews_per_session': 0,
                    'avg_visit_duration': 0,
                    'bounce_rate': 0,
                    'bot_visits': 0,
                    'weekday_report': []  # Add empty weekday report for zero case
                },
                'performance': {
                    'avg_processing_time': 0,
                    'min_processing_time': 0,
                    'max_processing_time': 0
                },
                'traffic_sources': {
                    'referrer_report': [],
                    'source_types': {},
                    'utm_sources': {},
                    'utm_mediums': {},
                    'utm_campaigns': {}
                },
                'page_analytics': {
                    'url_pageviews': [],
                    'entry_pages': [],
                    'exit_pages': [],
                    'status_code_report': {}
                },
                'visitor_demographics': {
                    'country_report': [],
                    'device_report': [],
                    'browser_report': [],
                    'os_report': []
                },
                'time_series': {
                    'pageviews_over_time': {'labels': [], 'data': []},
                    'unique_visitors_over_time': {'labels': [], 'data': []}
                },
                'weekly_traffic_distribution': [],
                'hourly_traffic_distribution': [],
                'avg_visit_duration': 0
            }

        # Bot visits (separate count)
        bot_visits = Analytics.query.filter(
            Analytics.is_bot,  # Using direct boolean check instead of == True
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).count()

        # Get detailed bot user agents
        bot_details = db.session.query(
            Analytics.browser,  # Changed from bot_user_agent to browser
            func.count().label('count'),
            func.max(Analytics.timestamp).label('last_seen')
        ).filter(
            Analytics.is_bot,
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by(Analytics.browser).all()  # Group by browser instead of bot_user_agent

        # Device type distribution with NULL handling (excluding bots)
        device_query = db.session.query(
            case(
                (Analytics.device_type == None, "unknown"),
                (Analytics.device_type == "", "unknown"),
                else_=func.lower(Analytics.device_type)
            ).label('device_type'),
            func.count().label('count')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by('device_type').order_by(desc('count')).all()

        device_report = [{
            'device': device,
            'total_visits': count,
            'unique_visitors': 0,
            'percentage': round((count / total_visits) * 100, 2)
        } for device, count in device_query]

        # Browser distribution (excluding bots)
        browser_query = db.session.query(
            func.coalesce(Analytics.browser, 'Unknown').label('browser'),
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot  # Using ~ operator instead of == False
        )

        # Apply date filtering if dates are provided
        if start_date_inclusive and end_date_inclusive:
            browser_query = browser_query.filter(
                Analytics.timestamp >= start_date_inclusive,
                Analytics.timestamp <= end_date_inclusive
            )

        # Group by full browser string to get all results
        browser_query = browser_query.group_by('browser').order_by(desc('count'))

        # Execute query
        browser_results = browser_query.all()

        # Process browser results with full consolidation
        browser_totals = {}
        total_visits = 0

        # First pass: calculate total visits
        for browser, count, _ in browser_results:
            # Normalize browser name, removing version
            normalized_browser = normalize_browser(browser)

            # Aggregate visits
            browser_totals[normalized_browser] = browser_totals.get(normalized_browser, 0) + count
            total_visits += count

        # Create consolidated report
        browser_report = []
        for normalized_browser, total_count in browser_totals.items():
            browser_report.append({
                'browser': normalized_browser,
                'total_visits': total_count,
                'unique_visitors': 0,  # Approximation
                'percentage': round((total_count / total_visits) * 100, 2) if total_visits else 0
            })

        # Sort report by total visits in descending order
        browser_report.sort(key=lambda x: x['total_visits'], reverse=True)

        # OS distribution (excluding bots)
        os_query = db.session.query(
            func.coalesce(Analytics.bot_user_agent, 'Unknown').label('user_agent'),
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot  # Using ~ operator instead of == False
        )

        # Apply date filtering if dates are provided
        if start_date_inclusive and end_date_inclusive:
            os_query = os_query.filter(
                Analytics.timestamp >= start_date_inclusive,
                Analytics.timestamp <= end_date_inclusive
            )

        # Group by full user agent string
        os_query = os_query.group_by('user_agent').order_by(desc('count'))

        # Execute query
        os_results = os_query.all()

        # Process OS results with full consolidation
        os_totals = {}
        total_os_visits = 0

        # First pass: calculate total visits
        for user_agent, count, _ in os_results:
            # Normalize OS name, removing version
            def normalize_os(user_agent_string):
                """
                Normalize OS name from user agent string

                Args:
                    user_agent_string (str): Full user agent string

                Returns:
                    str: Normalized OS name
                """
                if not user_agent_string or user_agent_string.lower() == 'unknown':
                    return 'Unknown'

                # Convert to lowercase
                user_agent_string = user_agent_string.lower()

                # OS detection mappings
                os_mappings = {
                    'Android': ['android'],
                    'iOS': ['ios', 'iphone', 'ipad'],
                    'Windows': ['windows', 'win'],
                    'MacOS': ['macintosh','mac', 'macos', 'mac os', 'mac osx', 'macosx'],
                    'Linux': ['linux', 'ubuntu', 'fedora', 'debian', 'centos', 'arch', 'mint', 'suse', 'pop!_os', 'manjaro', 'kali', 'opensuse']
                }

                # Check for known OS families
                for canonical_name, variations in os_mappings.items():
                    for variation in variations:
                        if variation in user_agent_string:
                            return canonical_name

                return 'Other'

            # Normalize OS name
            normalized_os = normalize_os(user_agent)

            # Aggregate visits
            os_totals[normalized_os] = os_totals.get(normalized_os, 0) + count
            total_os_visits += count

        # Create consolidated report
        os_report = []
        for normalized_os, total_count in os_totals.items():
            os_report.append({
                'os': normalized_os,
                'total_visits': total_count,
                'unique_visitors': 0,  # Approximation
                'percentage': round((total_count / total_os_visits) * 100, 2) if total_os_visits else 0
            })

        # Sort report by total visits in descending order
        os_report.sort(key=lambda x: x['total_visits'], reverse=True)

        # Country distribution (excluding bots)
        country_query = db.session.query(
            case(
                (Analytics.country == None, "unknown"),
                (Analytics.country == "", "unknown"),
                else_=func.lower(Analytics.country)
            ).label('country'),
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by('country').order_by(desc('count')).all()

        country_report = [{
            'country': country,
            'total_visits': count,
            'unique_visitors': visitors,
            'percentage': round((count / total_visits) * 100, 2)
        } for country, count, visitors in country_query]

        # Performance metrics with NULL and zero handling (excluding bots)
        performance_metrics = db.session.query(
            func.avg(func.coalesce(Analytics.processing_time, 0)).label('avg_processing_time'),
            func.min(func.coalesce(Analytics.processing_time, 0)).label('min_processing_time'),
            func.max(func.coalesce(Analytics.processing_time, 0)).label('max_processing_time')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).first()

        avg_processing_time = round(float(performance_metrics.avg_processing_time), 3) if performance_metrics.avg_processing_time else 0
        min_processing_time = round(float(performance_metrics.min_processing_time), 3) if performance_metrics.min_processing_time else 0
        max_processing_time = round(float(performance_metrics.max_processing_time), 3) if performance_metrics.max_processing_time else 0

        try:
            # Get first and last timestamp for each session (excluding bots)
            session_timestamps = db.session.query(
                Analytics.sessionid,
                func.min(Analytics.timestamp).label('first_visit'),
                func.max(Analytics.timestamp).label('last_visit')
            ).filter(
                ~Analytics.is_bot,  # Using ~ operator instead of == False
                Analytics.timestamp >= start_date_inclusive,
                Analytics.timestamp <= end_date_inclusive
            ).group_by(Analytics.sessionid).all()

            # Calculate average visit duration in minutes
            avg_visit_duration = 0
            if session_timestamps:
                total_duration = 0
                valid_sessions = 0
                for session in session_timestamps:
                    try:
                        if session.first_visit and session.last_visit:
                            # Convert duration to minutes
                            duration = (session.last_visit - session.first_visit).total_seconds() / 60
                            total_duration += duration
                            valid_sessions += 1
                    except (AttributeError, TypeError) as e:
                        current_app.logger.warning(f"Failed to process duration for session: {session.sessionid}, Error: {e}")
                        continue

                avg_visit_duration = round(total_duration / valid_sessions if valid_sessions > 0 else 0, 2)
        except SQLAlchemyError as e:
            current_app.logger.error(f"Database error calculating session durations: {e}")
            avg_visit_duration = 0

        # Get weekday distribution
        weekday_query = db.session.query(
            # SQLite returns 0=Sunday to 6=Saturday
            # We'll convert it to 1=Monday to 7=Sunday
            case(
                (func.strftime('%w', Analytics.timestamp) == '0', '7'),  # Sunday becomes 7
                else_=func.strftime('%w', Analytics.timestamp)
            ).label('weekday'),
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by('weekday').order_by('weekday').all()

        # Map weekday numbers to names (1=Monday to 7=Sunday)
        weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        weekday_stats = []

        # Initialize counts for all weekdays (1-7)
        weekday_counts = {str(i): {'count': 0, 'unique_visitors': 0} for i in range(1, 8)}

        # Fill in actual counts
        for weekday, count, visitors in weekday_query:
            weekday_counts[weekday] = {'count': count, 'unique_visitors': visitors}

        # Create final report with all weekdays (Monday to Sunday)
        for i in range(1, 8):
            weekday_stats.append({
                'weekday': weekday_names[i-1],
                'visits': weekday_counts[str(i)]['count'],
                'unique_visitors': weekday_counts[str(i)]['unique_visitors'],
                'percentage': round((weekday_counts[str(i)]['count'] / total_visits) * 100, 2) if total_visits > 0 else 0
            })

        # Bounce rate (sessions with only one page view, excluding bots)
        session_page_counts = db.session.query(
            Analytics.sessionid,
            func.count(Analytics.id).label('page_count')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by(Analytics.sessionid).all()

        single_page_sessions = sum(1 for session in session_page_counts if session.page_count == 1)
        bounce_rate = round((single_page_sessions / len(session_page_counts)) * 100, 2) if session_page_counts else 0

        # Get URL pageviews (excluding bots and stripping parameters)
        url_pageviews = db.session.query(
            case(
                (Analytics.url == None, "unknown"),
                (func.instr(Analytics.url, '?') > 0,
                 func.substr(Analytics.url, 1, func.instr(Analytics.url, '?') - 1)),
                else_=Analytics.url
            ).label('clean_url'),
            func.count().label('views'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by('clean_url').order_by(desc('views')).all()

        url_stats = [{
            'url': url,
            'total_visits': views,
            'unique_visitors': visitors,
            'percentage': round((views / total_visits) * 100, 2)
        } for url, views, visitors in url_pageviews]

        # Status Code Report (with URL and Referrer details)
        status_code_query = db.session.query(
            Analytics.status_code,
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            Analytics.status_code.isnot(None),
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by(Analytics.status_code).order_by(desc('count')).all()

        # Calculate total requests with status codes for correct percentage calculation
        total_status_code_requests = sum(count for _, count, _ in status_code_query)

        status_code_details = []
        for status_code, count, unique_visitors in status_code_query:
            # Get top URLs for this status code
            top_urls = db.session.query(
                Analytics.url,
                func.count().label('url_count')
            ).filter(
                Analytics.status_code == status_code,
                Analytics.timestamp >= start_date_inclusive,
                Analytics.timestamp <= end_date_inclusive
            ).group_by(Analytics.url).order_by(desc('url_count')).limit(5).all()

            # Get top referrers for this status code
            top_referrers = db.session.query(
                Analytics.referrer,
                func.count().label('referrer_count')
            ).filter(
                Analytics.status_code == status_code,
                Analytics.referrer.isnot(None),
                Analytics.timestamp >= start_date_inclusive,
                Analytics.timestamp <= end_date_inclusive
            ).group_by(Analytics.referrer).order_by(desc('referrer_count')).limit(5).all()

            status_code_details.append({
                'status_code': status_code,
                'count': count,
                'unique_visitors': unique_visitors,
                'percentage': round((count / total_status_code_requests) * 100, 2),
                'top_urls': [{'url': url, 'count': url_count} for url, url_count in top_urls],
                'top_referrers': [{'referrer': referrer, 'count': referrer_count} for referrer, referrer_count in top_referrers]
            })

        # Get referrer report with domain extraction (only first entry per session)
        referrer_query = db.session.query(
            case(
                (Analytics.referrer == None, 'direct'),
                (Analytics.referrer == '', 'direct'),
                else_=case(
                    # First try to extract domain after http:// or https://
                    (func.instr(Analytics.referrer, '://') > 0,
                     func.substr(
                         Analytics.referrer,
                         func.instr(Analytics.referrer, '://') + 3,
                         case(
                             (func.instr(func.substr(Analytics.referrer, func.instr(Analytics.referrer, '://') + 3), '/') > 0,
                              func.instr(func.substr(Analytics.referrer, func.instr(Analytics.referrer, '://') + 3), '/') - 1),
                             else_=999999
                         )
                     )),
                    # If no protocol found, take everything up to the first slash
                    else_=case(
                        (func.instr(Analytics.referrer, '/') > 0,
                         func.substr(Analytics.referrer, 1, func.instr(Analytics.referrer, '/') - 1)),
                        else_=Analytics.referrer
                    )
                )
            ).label('domain'),
            Analytics.referrer,
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive,
            Analytics.id.in_(
                db.session.query(func.min(Analytics.id)).filter(
                    ~Analytics.is_bot,  # Using ~ operator instead of == False
                    Analytics.timestamp >= start_date_inclusive,
                    Analytics.timestamp <= end_date_inclusive
                ).group_by(Analytics.sessionid)
            )
        ).group_by('domain', Analytics.referrer).order_by(desc('count')).all()

        # Process referrers into a hierarchical structure
        domain_stats = {}
        for domain, full_url, count, unique_visitors in referrer_query:
            # Clean up the domain
            if domain:
                domain = domain.lower()
                # Remove www. prefix if present
                if domain.startswith('www.'):
                    domain = domain[4:]

                # Remove port number if present
                if ':' in domain:
                    domain = domain.split(':', 1)[0]

                # Remove trailing slash if present
                if domain.endswith('/'):
                    domain = domain.rstrip('/')

            if not domain or domain == '':
                domain = 'direct'

            if domain not in domain_stats:
                domain_stats[domain] = {
                    'total_count': 0,
                    'total_unique_visitors': 0,
                    'urls': []
                }

            domain_stats[domain]['total_count'] += count
            domain_stats[domain]['total_unique_visitors'] += unique_visitors

            if full_url:  # Skip None values
                domain_stats[domain]['urls'].append({
                    'url': full_url,
                    'count': count,
                    'unique_visitors': unique_visitors
                })

        # Convert to list and calculate percentages
        referrer_report = []
        total_referrals = sum(stats['total_count'] for stats in domain_stats.values())

        for domain, stats in sorted(domain_stats.items(), key=lambda x: x[1]['total_count'], reverse=True):
            referrer_report.append({
                'domain': domain,
                'total_visits': stats['total_count'],
                'unique_visitors': stats['total_unique_visitors'],
                'percentage': round((stats['total_count'] / total_referrals) * 100, 2),
                'urls': sorted(stats['urls'], key=lambda x: x['count'], reverse=True)
            })

        # All Pages Report
        def strip_query_params(url):
            """Remove query parameters from URL"""
            if not url:
                return url
            return url.split('?')[0]

        # First get base URLs without query parameters
        all_pages_query = db.session.query(
            func.coalesce(func.substr(Analytics.url, 1, func.instr(Analytics.url + '?', '?') - 1), Analytics.url).label('base_url'),
            func.count(Analytics.id).label('hits'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.url != None,
            Analytics.url != ''
        )

        if start_date_inclusive and end_date_inclusive:
            all_pages_query = all_pages_query.filter(
                Analytics.timestamp >= start_date_inclusive,
                Analytics.timestamp <= end_date_inclusive
            )

        all_pages_query = all_pages_query.group_by('base_url')
        all_pages_results = all_pages_query.all()

        # Calculate total hits for percentage
        total_hits = sum(result.hits for result in all_pages_results)

        # Format all pages report
        all_pages_report = [{
            'url': result.base_url,
            'hits': result.hits,
            'unique_visitors': result.unique_visitors,
            'percentage': round((result.hits / total_hits * 100), 2) if total_hits > 0 else 0
        } for result in all_pages_results]

        # Sort by hits descending
        all_pages_report.sort(key=lambda x: x['hits'], reverse=True)

        # Entry Pages Report
        entry_pages_query = db.session.query(
            case(
                (Analytics.url == None, "unknown"),
                (func.instr(Analytics.url, '?') > 0,
                 func.substr(Analytics.url, 1, func.instr(Analytics.url, '?') - 1)),
                else_=Analytics.url
            ).label('entry_url'),
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive,
            Analytics.id.in_(
                db.session.query(func.min(Analytics.id)).filter(
                    ~Analytics.is_bot,  # Using ~ operator instead of == False
                    Analytics.timestamp >= start_date_inclusive,
                    Analytics.timestamp <= end_date_inclusive
                ).group_by(Analytics.sessionid)
            )
        ).group_by('entry_url').order_by(desc('count')).all()

        entry_pages = [{
            'url': url,
            'total_visits': count,
            'unique_visitors': visitors,
            'percentage': round((count / len(session_timestamps)) * 100, 2) if session_timestamps else 0
        } for url, count, visitors in entry_pages_query]

        exit_pages_query = db.session.query(
            case(
                (Analytics.url == None, "unknown"),
                (func.instr(Analytics.url, '?') > 0,
                 func.substr(Analytics.url, 1, func.instr(Analytics.url, '?') - 1)),
                else_=Analytics.url
            ).label('exit_url'),
            func.count().label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive,
            Analytics.id.in_(
                db.session.query(func.max(Analytics.id)).filter(
                    ~Analytics.is_bot,  # Using ~ operator instead of == False
                    Analytics.timestamp >= start_date_inclusive,
                    Analytics.timestamp <= end_date_inclusive
                ).group_by(Analytics.sessionid)
            )
        ).group_by('exit_url').order_by(desc('count')).all()

        exit_pages = [{
            'url': url,
            'total_visits': count,
            'unique_visitors': visitors,
            'percentage': round((count / len(session_timestamps)) * 100, 2) if session_timestamps else 0
        } for url, count, visitors in exit_pages_query]

        # Add status code report to the page_analytics section
        page_analytics = {
            'url_pageviews': url_stats,
            'entry_pages': entry_pages,
            'exit_pages': exit_pages,
            'status_code_report': status_code_details,
            'all_pages': all_pages_report
        }

        # Advertising/UTM Source Analysis
        def extract_utm_source(url):
            """Extract UTM source from URL query parameters"""
            try:
                parsed_url = urlparse(url)
                query_params = parse_qs(parsed_url.query)
                utm_sources = query_params.get('utm_source', [])
                return utm_sources[0] if utm_sources else None
            except Exception:
                return None

        # Get all URLs with their visit counts
        utm_query = db.session.query(
            Analytics.url,
            func.count(Analytics.id).label('count'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.url.like('%utm_source=%')  # Only include URLs with utm_source
        )

        # Always apply date filtering - this is required for correct UTM source reporting
        # when date filters are applied in the dashboard

        # Double-check that we have valid dates before applying filter
        if start_date_inclusive is None or end_date_inclusive is None:
            current_app.logger.warning("Missing date range for UTM query, using default 30-day range")
            end_date_inclusive = datetime.now()
            start_date_inclusive = end_date_inclusive - timedelta(days=30)

        utm_query = utm_query.filter(
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        )

        # Group by full URL to get all visits
        utm_query = utm_query.group_by(Analytics.url)

        # Execute query
        utm_results = utm_query.all()

        # Log UTM query results for debugging
        current_app.logger.info(f"UTM query returned {len(utm_results)} results for date range: {start_date_inclusive} to {end_date_inclusive}")

        # Process results to aggregate by UTM source
        utm_sources = {}
        total_utm_visits = 0

        # First pass: extract and aggregate UTM sources
        for url, count, unique_visitors in utm_results:
            utm_source = extract_utm_source(url)
            if utm_source:  # Only process if utm_source is present
                if utm_source not in utm_sources:
                    utm_sources[utm_source] = {
                        'total_visits': 0,
                        'unique_visitors': 0
                    }

                utm_sources[utm_source]['total_visits'] += count
                utm_sources[utm_source]['unique_visitors'] += unique_visitors
                total_utm_visits += count

        # Create consolidated UTM source report
        utm_report = []
        for source, data in utm_sources.items():
            utm_report.append({
                'source': source,
                'total_visits': data['total_visits'],
                'unique_visitors': data['unique_visitors'],
                'percentage': round((data['total_visits'] / total_utm_visits) * 100, 2) if total_utm_visits else 0
            })

        # Sort UTM source report by total visits
        utm_report.sort(key=lambda x: x['total_visits'], reverse=True)

        # Log the final UTM report for debugging
        current_app.logger.info(f"Final UTM report contains {len(utm_report)} sources with {total_utm_visits} total visits")

        # Generate time series data
        current_app.logger.info(f"Generating time series data from {start_date_inclusive} to {end_date_inclusive}")

        # Get daily pageviews and unique visitors
        daily_stats = db.session.query(
            func.date(Analytics.timestamp).label('date'),
            func.count().label('pageviews'),
            func.count(Analytics.sessionid.distinct()).label('unique_visitors')
        ).filter(
            ~Analytics.is_bot,  # Using ~ operator instead of == False
            Analytics.timestamp >= start_date_inclusive,
            Analytics.timestamp <= end_date_inclusive
        ).group_by('date').order_by('date').all()

        current_app.logger.info(f"Pageviews query result: {[(str(day.date), day.pageviews) for day in daily_stats]}")
        current_app.logger.info(f"Unique visitors query result: {[(str(day.date), day.unique_visitors) for day in daily_stats]}")

        # Format data for charts
        pageviews_data = {
            'labels': [str(day.date) for day in daily_stats],
            'data': [day.pageviews for day in daily_stats]
        }

        unique_visitors_data = {
            'labels': [str(day.date) for day in daily_stats],
            'data': [day.unique_visitors for day in daily_stats]
        }

        current_app.logger.info(f"Pageviews data: {pageviews_data}")
        current_app.logger.info(f"Unique visitors data: {unique_visitors_data}")

        # Add time series data to the response
        time_series = {
            'pageviews_over_time': pageviews_data,
            'unique_visitors_over_time': unique_visitors_data
        }

        return {
            # Overview metrics
            'overview': {
                'total_pageviews': total_visits,
                'unique_visitors': total_unique_visitors,  # Use the subquery result
                'pageviews_per_session': pages_per_session,
                'avg_visit_duration': avg_visit_duration,
                'bounce_rate': bounce_rate,
                'bot_visits': bot_visits,
                'bot_details': [{'user_agent': agent, 'count': count, 'last_seen': last_seen.isoformat()} for agent, count, last_seen in bot_details],
                'weekday_report': weekday_stats
            },

            # Performance metrics
            'performance': {
                'avg_processing_time': avg_processing_time,
                'min_processing_time': min_processing_time,
                'max_processing_time': max_processing_time
            },

            # Traffic sources
            'traffic_sources': {
                'referrer_report': referrer_report,
                'source_types': {},
                'utm_sources': utm_report,
                'utm_mediums': {},
                'utm_campaigns': {}
            },

            # Page analytics
            'page_analytics': page_analytics,

            # Visitor demographics
            'visitor_demographics': {
                'country_report': country_report,
                'device_report': device_report,
                'browser_report': browser_report,
                'os_report': os_report
            },

            # Time series data
            'time_series': time_series,

            # Weekly and hourly distribution
            'weekly_traffic_distribution': [weekday_counts[str(i)]['count'] for i in range(1, 8)],
            'hourly_traffic_distribution': [0] * 24,  # Initialize with zeros
            'avg_visit_duration': avg_visit_duration
        }

    except Exception as e:
        logger.error(f"Error in get_analytics_data: {str(e)}")
        return {'error': 'Internal server error'}, 500

def register_analytics_routes(app):
    """
    Register analytics-related routes with authentication

    Args:
        app (Flask): Flask application instance
    """
    @app.route('/api/analytics/data')
    def get_analytics():
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        return jsonify(get_analytics_data(start_date, end_date))