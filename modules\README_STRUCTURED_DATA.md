# Structured Data Implementation for SEO

This document explains how structured data (JSON-LD) has been implemented for SEO purposes on the Ketolabben website.

## Overview

Structured data has been implemented using JSON-LD format to enhance search engine understanding of the website content. The implementation is designed to be separate from the templates and CMS database entries, making it easy to maintain and update.

## Features

- **Automatic Detection**: The system automatically detects the type of page being viewed and generates appropriate structured data.
- **Multiple Schema Types**: Supports various schema types including:
  - Recipe schema for recipe pages
  - Article schema for blog posts
  - Product schema for product pages
  - Book schema for book pages
  - Review schema for review pages
  - Organization schema for all pages
  - Website schema for all pages
- **Non-Intrusive**: Structured data is injected into HTML responses without modifying templates or database entries.

## Implementation Details

### Files

- `modules/structured_data.py`: Contains functions to generate JSON-LD structured data for different content types.
- `modules/seo_data_extractor.py`: Contains functions to extract data from the current page context and determine page types.
- `run.py`: Modified to include middleware that injects structured data into HTML responses.
- `utilities/test_structured_data.py`: Test script to verify structured data generation.
- `utilities/test_page_detection.py`: Test script to verify page type detection.

### How It Works

1. When a page is requested, the Flask application processes the request and generates an HTML response.
2. Before sending the response, the `process_response` middleware in `run.py` processes the HTML content:
   - First, it processes images using the existing `process_images` function.
   - Then, it adds structured data using the `add_structured_data` function.
3. The `add_structured_data` function:
   - Determines the type of page being viewed using `get_page_type` from `seo_data_extractor.py`.
   - Extracts relevant data from the page context using functions from `seo_data_extractor.py`.
   - Generates appropriate JSON-LD structured data using functions from `structured_data.py`.
   - Injects the structured data into the HTML response using `inject_structured_data`.

### Page Type Detection

The system uses multiple methods to accurately determine the type of page being viewed:

1. **Flask Request Context**: Examines the current Flask endpoint and route.
2. **Content Analysis**: Checks the CMS post type if available.
3. **URL Pattern Matching**: Analyzes URL patterns as a fallback method.

This multi-layered approach ensures accurate page type detection even when URL patterns change or are ambiguous.

### Supported Schema Types

1. **Recipe Schema**:
   - Used for recipe pages (post_type = 'oppskrift').
   - Includes recipe name, description, author, image, ingredients, instructions, cooking time, etc.

2. **Article Schema**:
   - Used for blog posts (post_type = 'artikkel').
   - Includes article title, description, author, image, publication date, etc.

3. **Product Schema**:
   - Used for product pages.
   - Includes product name, description, image, price, reviews, etc.

4. **Book Schema**:
   - Used for book pages.
   - Includes book title, author, description, ISBN, price, reviews, etc.

5. **Review Schema**:
   - Used for review pages.
   - Includes review ratings, author, content, etc.

6. **Organization Schema**:
   - Added to all pages.
   - Includes organization name, logo, social media links, etc.

7. **Website Schema**:
   - Added to all pages.
   - Includes website name, URL, search action, etc.

## Testing

You can test the structured data generation using the `utilities/test_structured_data.py` script:

```bash
cd /path/to/ketolabben
python utilities/test_structured_data.py
```

## Validation

You can validate the structured data on your website using Google's Structured Data Testing Tool or Rich Results Test:

- [Google Rich Results Test](https://search.google.com/test/rich-results)
- [Schema.org Validator](https://validator.schema.org/)

## Customization

To customize the structured data generation:

1. Modify the functions in `modules/structured_data.py` to change the structure or content of the JSON-LD data.
2. Modify the functions in `modules/seo_data_extractor.py` to change how data is extracted from the page context.
3. Modify the `add_structured_data` function in `run.py` to change which structured data is added to which pages.

## Future Improvements

Potential future improvements include:

1. Adding more schema types (e.g., FAQs, How-to guides, etc.).
2. Enhancing data extraction to include more detailed information.
3. Adding a caching mechanism to improve performance.
4. Creating an admin interface to customize structured data settings.