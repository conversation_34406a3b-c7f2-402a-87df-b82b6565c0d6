#! /usr/bin/env python3.6
# Python 3.6 or newer required.
from flask import Blueprint, jsonify, request, current_app
import json
import os
import stripe
import logging
from datetime import datetime
from modules.models import db, CustomerData
from modules.payment import send_confirmation_email
from sqlalchemy.exc import SQLAlchemyError
import secrets

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Blueprint for webhook
webhook_bp = Blueprint('webhook', __name__)

# This is your secret API key.
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

# Replace this endpoint secret with your endpoint's unique secret
# If you are testing with the CLI, find the secret by running 'stripe listen'
# If you are using an endpoint defined with the API or dashboard, look in your webhook settings
# at https://dashboard.stripe.com/webhooks
endpoint_secret = os.getenv("STRIPE_ENDPOINT_SECRET")

def save_webhook_debug_data(event_data, event_type):
    """
    Save raw webhook JSON data to a text file for debugging purposes
    
    Args:
        event_data (dict): The webhook event data
        event_type (str): The type of webhook event
    """
    try:
        # Create debug directory if it doesn't exist
        debug_dir = os.path.join(os.path.dirname(__file__), '..', 'instance', 'webhook_debug')
        os.makedirs(debug_dir, exist_ok=True)
        
        # Create filename with timestamp and event type
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"webhook_{event_type}_{timestamp}.json"
        filepath = os.path.join(debug_dir, filename)
        
        # Save the raw JSON data
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(event_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Webhook debug data saved to: {filepath}")
        
    except Exception as e:
        logger.error(f"Failed to save webhook debug data: {str(e)}")

def send_webhook_confirmation_email(customer_email, customer_name, download_code):
    """
    Send confirmation email for webhook-processed orders
    
    Args:
        customer_email (str): Customer's email address
        customer_name (str): Customer's name
        download_code (str): Unique download code (Stripe session ID)
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        from flask_mail import Message
        from flask import render_template, url_for
        
        mail = current_app.extensions['mail']
        
        # Generate download link using the download code
        download_link = url_for('download.download_by_code', 
                               download_code=download_code, 
                               _external=True)
        
        # Use the properly formatted sender from app config
        msg = Message(
            "Ordrebekreftelse - Ketolabben",
            sender=current_app.config['MAIL_DEFAULT_SENDER'],
            recipients=[customer_email]
        )

        msg.body = f"Hei {customer_name},\n\nTakk for din bestilling! Du kan laste ned din e-bok her: {download_link}\n\nMed vennlig hilsen,\nKetolabben"
        
        # Try to render the template, fall back to plain text if it fails
        try:
            html_content = render_template(
                'ordrebekreftelse-email.html',
                download_link=download_link,
                email=customer_email,
                customer_name=customer_name
            )
            
            # Process the HTML to add line breaks to base64 encoded images
            import re
            
            def add_line_breaks_to_base64(match):
                """Add line breaks every 76 characters to base64 content"""
                prefix = match.group(1)
                base64_data = match.group(2)
                chunks = [base64_data[i:i+76] for i in range(0, len(base64_data), 76)]
                formatted_base64 = '\n'.join(chunks)
                return prefix + formatted_base64
            
            pattern = r'(data:image\/[^;]+;base64,)([A-Za-z0-9+/=]+)'
            html_content = re.sub(pattern, add_line_breaks_to_base64, html_content)
            msg.html = html_content
            
        except Exception as template_error:
            logger.warning(f"Could not render email template: {str(template_error)}")
            # Continue with plain text email
        
        mail.send(msg)
        logger.info(f"Webhook confirmation email sent to {customer_email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send webhook confirmation email: {str(e)}")
        return False

def handle_checkout_session_completed(session):
    """
    Handle completed checkout session by storing customer data in database
    
    Args:
        session (dict): Stripe checkout session object
    """
    try:
        # Extract customer information
        customer_email = session.get('customer_details', {}).get('email', 'Unknown')
        customer_name = session.get('customer_details', {}).get('name', 'Unknown')
        session_id = session.get('id', '')
        payment_status = session.get('payment_status', 'paid')
        
        logger.info(f"Processing checkout session completed for {customer_email}")
        
        # Check if this session has already been processed
        existing_record = CustomerData.query.filter_by(download_code=session_id).first()
        if existing_record:
            logger.warning(f"Checkout session {session_id} already processed, skipping")
            return True
        
        # Generate a unique download code (using session ID as it's already unique)
        download_code = session_id
        
        # Generate a unique Flask session ID for webhook orders
        webhook_flask_session_id = f"webhook_{secrets.token_urlsafe(16)}"
        
        # Create customer record
        customer_data = CustomerData(
            customer_name=customer_name,
            customer_email=customer_email,
            payment_status=payment_status,
            download_code=download_code,
            timestamp=datetime.now(),
            download_count=0,
            email_sent=False,
            flask_session_id=webhook_flask_session_id  # Use generated session ID for webhook orders
        )
        
        # Save to database
        db.session.add(customer_data)
        db.session.commit()
        
        logger.info(f"Successfully created customer record for {customer_email} with session {session_id}")
        
        # Send confirmation email with webhook-specific function
        try:
            if send_webhook_confirmation_email(customer_email, customer_name, download_code):
                customer_data.email_sent = True
                db.session.commit()
                logger.info(f"Confirmation email sent to {customer_email}")
            else:
                logger.warning(f"Failed to send confirmation email to {customer_email}")
        except Exception as email_error:
            logger.error(f"Error sending confirmation email: {str(email_error)}")
        
        return True
        
    except SQLAlchemyError as e:
        db.session.rollback()
        logger.error(f"Database error processing checkout session: {str(e)}")
        return False
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error processing checkout session: {str(e)}")
        return False

@webhook_bp.route('/webhook', methods=['POST'])
def webhook():
    event = None
    payload = request.data
    
    logger.info("Webhook received")

    try:
        event = json.loads(payload)
    except json.decoder.JSONDecodeError as e:
        logger.error(f'⚠️  Webhook error while parsing basic request: {str(e)}')
        return jsonify(success=False), 400
        
    if endpoint_secret:
        # Only verify the event if there is an endpoint secret defined
        # Otherwise use the basic event deserialized with json
        sig_header = request.headers.get('stripe-signature')
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except stripe.error.SignatureVerificationError as e:
            logger.error(f'⚠️  Webhook signature verification failed: {str(e)}')
            return jsonify(success=False), 400

    # Save debug data for all webhook events
    event_type = event.get('type', 'unknown')
    save_webhook_debug_data(event, event_type)

    # Handle the event
    if event and event['type'] == 'checkout.session.completed':
        session = event['data']['object']  # contains a stripe.checkout.Session
        logger.info(f'Checkout session completed for session {session.get("id", "unknown")}')
        
        # Handle the completed checkout session
        success = handle_checkout_session_completed(session)
        if not success:
            logger.error("Failed to process checkout session completed event")
            return jsonify(success=False), 500
            
    elif event['type'] == 'payment_intent.succeeded':
        payment_intent = event['data']['object']  # contains a stripe.PaymentIntent
        logger.info(f'Payment for {payment_intent["amount"]} succeeded')
        # You can add additional handling here if needed
        
    elif event['type'] == 'payment_method.attached':
        payment_method = event['data']['object']  # contains a stripe.PaymentMethod
        logger.info(f'Payment method {payment_method.get("id", "unknown")} attached')
        # You can add additional handling here if needed
        
    else:
        # Log unexpected event types but don't fail
        logger.info(f'Received unhandled event type: {event_type}')

    return jsonify(success=True)