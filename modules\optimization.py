from flask import Flask
from flask_assets import Environment, Bundle, Filter
from flask_compress import Compress
import htmlmin
import os
import logging
import re
from flask import request, send_file, jsonify
from PIL import Image
import io

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FontPathFixer(Filter):
    """Custom filter to fix font paths in CSS files."""
    
    def output(self, _in, out, **kwargs):
        content = _in.read()
        # Fix the font paths to point to the correct location
        content = content.replace('../fonts/', '../bootstrap/fonts/')
        out.write(content)

def minify_html(html):
    """
    Minify HTML content using htmlmin.
    
    Args:
        html (str): HTML content to minify
    
    Returns:
        str: Minified HTML content
    """
    return htmlmin.minify(
        html, 
        remove_comments=True,
        remove_empty_space=True,
        reduce_empty_attributes=True,
        remove_all_empty_space=True,
        reduce_boolean_attributes=True
    )

def minify_css_content(css_content):
    """
    Minify CSS content on-the-fly.
    
    Args:
        css_content (str): CSS content to minify
    
    Returns:
        str: Minified CSS content
    """
    try:
        import cssmin
        return cssmin.compress(css_content)
    except ImportError:
        logger.error("cssmin library not installed. Install it using 'pip install cssmin'")
        return css_content
    except Exception as e:
        logger.error(f"Error minifying CSS content: {e}")
        return css_content

def minify_js_content(js_content):
    """
    Minify JavaScript content on-the-fly.
    
    Args:
        js_content (str): JavaScript content to minify
    
    Returns:
        str: Minified JavaScript content
    """
    try:
        import jsmin
        return jsmin.jsmin(js_content)
    except ImportError:
        logger.error("jsmin library not installed. Install it using 'pip install jsmin'")
        return js_content
    except Exception as e:
        logger.error(f"Error minifying JavaScript content: {e}")
        return js_content

def ensure_dir(directory):
    """Ensure directory exists, create if it doesn't"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def init_assets(app: Flask):
    """Initialize Flask-Assets for managing and optimizing static assets."""
    print("Initializing Flask-Assets...")
    
    # Configure Flask-Assets
    assets = Environment(app)
    assets.debug = app.config.get('ASSETS_DEBUG', False)
    print(f"Assets debug mode: {assets.debug}")
    
    # Configure assets environment
    base_dir = os.path.dirname(os.path.dirname(__file__))
    app.static_folder = os.path.join(base_dir, 'assets')
    assets.directory = app.static_folder
    assets.url = app.static_url_path
    print(f"Assets directory: {assets.directory}")
    print(f"Assets URL: {assets.url}")
    
    # Ensure optimized directory exists
    output_dir = os.path.join(app.static_folder, 'optimized')
    ensure_dir(output_dir)
    print(f"Output directory: {output_dir}")
    
    # CSS Bundle with custom font path fixer
    print("Creating CSS bundle...")
    css_all = Bundle(
        'bootstrap/css/bootstrap.min.css',
        Bundle('bootstrap/css/bootstrap-icons.css', filters=FontPathFixer()),
        'css/animate.min.css',
        'css/bs-theme-overrides.css',
        'css/cms.css',
        'css/b-studio.css',
        'css/Lightbox-Gallery-baguetteBox.min.css',
        'css/custom-module-cards.css',
        'css/cookie.css',
        'css/Caveat.css',
        'css/Raleway.css',
        'css/Open Sans.css',
        'css/Shadows Into Light Two.css',
        output='optimized/styles.min.css'
    )
    assets.register('css_bundle', css_all)
    print("CSS bundle created and registered")

    # JavaScript Bundle
    print("Creating JavaScript bundle...")
    js_all = Bundle(
        'bootstrap/js/bootstrap.min.js',
        'js/bs-init.js',
        'js/cookie-banner.js',
        'js/Lightbox-Gallery-baguetteBox.min.js',
        'js/Lightbox-Gallery.js',
        'js/bot-detection.js',
         output='optimized/scripts.min.js'
    )
    assets.register('js_bundle', js_all)
    print("JavaScript bundle created and registered")
    
    try:
        # Force bundle building regardless of debug mode
        css_all.build(force=True)
        js_all.build(force=True)
        print("Asset bundles built successfully")
    except Exception as e:
        print(f"Error building assets: {str(e)}")
        raise

def serve_minified_assets(app: Flask):
    """
    Add a response handler to minify CSS and JavaScript files dynamically.
    """
    @app.after_request
    def minify_response(response):
        if response.mimetype == 'text/css':
            try:
                minified_content = minify_css_content(response.get_data(as_text=True))
                response.set_data(minified_content)
                response.headers['Content-Length'] = len(minified_content)
            except Exception as e:
                logger.error(f"Error in CSS minification middleware: {e}")
        elif response.mimetype == 'application/javascript':
            try:
                minified_content = minify_js_content(response.get_data(as_text=True))
                response.set_data(minified_content)
                response.headers['Content-Length'] = len(minified_content)
            except Exception as e:
                logger.error(f"Error in JavaScript minification middleware: {e}")
        return response

def init_compression(app: Flask):
    """Initialize Flask-Compress for HTTP-level compression."""
    logger.info("Initializing Flask-Compress...")
    
    # Initialize Flask-Compress
    Compress(app)
    
    # Configure compression settings
    app.config['COMPRESS_MIMETYPES'] = [
        'text/html',
        'text/css',
        'text/xml',
        'application/javascript',
        'application/json',
        'application/x-javascript',
        'application/xml'
    ]
    app.config['COMPRESS_LEVEL'] = 6  # Balanced compression level
    app.config['COMPRESS_MIN_SIZE'] = 500  # Only compress responses > 500 bytes
    
    logger.info("Flask-Compress initialized successfully")

def register_module(app: Flask):
    """Register the optimization module with the Flask application"""
    # Initialize both asset bundling and HTTP compression
    init_assets(app)
    serve_minified_assets(app)
    init_compression(app)
    
    @app.after_request
    def optimize_response(response):
        """Optimize response with HTML minification"""
        try:
            if response.content_type and 'text/html' in response.content_type:
                try:
                    original_size = len(response.get_data())
                    response.direct_passthrough = False
                    minified_data = minify_html(response.get_data(as_text=True))
                    response.set_data(minified_data)
                    minified_size = len(response.get_data())
                    logger.info(f"HTML Minification: {original_size:,} bytes -> {minified_size:,} bytes ({(1 - minified_size/original_size)*100:.1f}% reduction)")
                except Exception as e:
                    logger.error(f"HTML Minification failed: {str(e)}")
                    response.direct_passthrough = True
        except Exception as e:
            logger.error(f"Optimization error: {str(e)}")
        
        return response

    return app